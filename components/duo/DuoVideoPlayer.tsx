"use client"

import { useEffect, useRef, useState } from 'react'

type MediaVideo = {
  type: 'video'
  src_r2_key: string
  hls_manifest?: string | null
}

export function DuoVideoPlayer({
  partA,
  partB,
  signedPlayback = false,
  autoPlay = false,
  showControls = true,
  initiatorName,
  responderName,
  initiatorPhoto,
  responderPhoto,
  onPlay,
  finalVideoUrl,
  finalVideoR2Key,
}: {
  partA: MediaVideo
  partB?: MediaVideo
  signedPlayback?: boolean
  autoPlay?: boolean
  showControls?: boolean
  initiatorName?: string
  responderName?: string
  initiatorPhoto?: string
  responderPhoto?: string
  onPlay?: () => void
  finalVideoUrl?: string
  finalVideoR2Key?: string
}) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const fullscreenVideoRef = useRef<HTMLVideoElement>(null)
  const [muted, setMuted] = useState(false)
  const [playingB, setPlayingB] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showFullscreen, setShowFullscreen] = useState(false)
  const [currentPart, setCurrentPart] = useState(1)
  const [videoLoaded, setVideoLoaded] = useState(false)
  const [videoError, setVideoError] = useState(false)

  // Public fallback URLs
  const publicA = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partA.src_r2_key}`
  const publicB = partB ? `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partB.src_r2_key}` : undefined

  // Final processed video URL (for preview)
  const finalVideoPublicUrl = finalVideoR2Key
    ? `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${finalVideoR2Key}`
    : finalVideoUrl

  // Signed URLs state
  const [signedA, setSignedA] = useState<string | null>(null)
  const [signedB, setSignedB] = useState<string | null>(null)

  // Fetch signed GET URLs (only when enabled)
  useEffect(() => {
    if (!signedPlayback) return
    let cancelled = false
    async function sign() {
      try {
        const resA = await fetch(`/api/media/sign?key=${encodeURIComponent(partA.src_r2_key)}`)
        if (resA.ok) {
          const j = await resA.json()
          if (!cancelled) setSignedA(j.url)
        }
        if (partB) {
          const resB = await fetch(`/api/media/sign?key=${encodeURIComponent(partB.src_r2_key)}`)
          if (resB.ok) {
            const j2 = await resB.json()
            if (!cancelled) setSignedB(j2.url)
          }
        }
      } catch {
        // ignore; fallback to public URLs
      }
    }
    sign()
    return () => { cancelled = true }
  }, [signedPlayback, partA.src_r2_key, partB?.src_r2_key])

  useEffect(() => {
    const el = videoRef.current
    if (!el) return

    const onEnded = () => {
      setIsPlaying(false)
      if (partB && !playingB) {
        setPlayingB(true)
        setCurrentPart(2)
        const nextSrc = signedB || publicB
        if (nextSrc) {
          el.src = nextSrc
          el.currentTime = 0
          el.play().catch(() => {})
          setIsPlaying(true)
        }
      } else {
        // End of Part B - reset for next play
        setPlayingB(false)
        setCurrentPart(1)
        // Reset video source back to Part A
        const firstSrc = signedA || publicA
        if (firstSrc) {
          el.src = firstSrc
          el.currentTime = 0
        }
      }
    }

    const handlePlay = () => {
      setIsPlaying(true)
      onPlay?.() // Call the view tracking callback
    }
    const handlePause = () => setIsPlaying(false)

    el.addEventListener('ended', onEnded)
    el.addEventListener('play', handlePlay)
    el.addEventListener('pause', handlePause)
    return () => {
      el.removeEventListener('ended', onEnded)
      el.removeEventListener('play', handlePlay)
      el.removeEventListener('pause', handlePause)
    }
  }, [partB, playingB, signedB, publicB])

  // IDENTICAL logic for mobile fullscreen video
  useEffect(() => {
    const el = fullscreenVideoRef.current
    if (!el || !showFullscreen) return

    const onEnded = () => {
      setIsPlaying(false)
      if (partB && !playingB) {
        setPlayingB(true)
        setCurrentPart(2)
        const nextSrc = signedB || publicB
        if (nextSrc) {
          el.src = nextSrc
          el.currentTime = 0
          el.play().catch(() => {})
          setIsPlaying(true)
        }
      } else {
        // End of Part B - reset for next play
        setPlayingB(false)
        setCurrentPart(1)
        // Reset video source back to Part A
        const firstSrc = signedA || publicA
        if (firstSrc) {
          el.src = firstSrc
          el.currentTime = 0
        }
      }
    }

    const handlePlay = () => {
      setIsPlaying(true)
      onPlay?.() // Call the view tracking callback
    }
    const handlePause = () => setIsPlaying(false)

    el.addEventListener('ended', onEnded)
    el.addEventListener('play', handlePlay)
    el.addEventListener('pause', handlePause)
    return () => {
      el.removeEventListener('ended', onEnded)
      el.removeEventListener('play', handlePlay)
      el.removeEventListener('pause', handlePause)
    }
  }, [partB, playingB, signedB, publicB, showFullscreen, signedA, publicA, onPlay])

  // Load first frame for preview - Enhanced for mobile compatibility
  useEffect(() => {
    const el = videoRef.current
    if (!el) return

    setVideoLoaded(false)
    setVideoError(false)

    const handleLoadedMetadata = () => {
      // Set to first frame to show preview - use longer time for final video
      const seekTime = finalVideoPublicUrl ? 1.0 : 0.1
      el.currentTime = seekTime
    }

    const handleLoadedData = () => {
      setVideoLoaded(true)
      // Ensure we have a visible frame - more aggressive for mobile
      const seekTime = finalVideoPublicUrl ? 1.0 : 0.1
      if (el.currentTime === 0 || Math.abs(el.currentTime - seekTime) > 0.5) {
        el.currentTime = seekTime
      }
    }

    const handleCanPlay = () => {
      setVideoLoaded(true)
      // Additional mobile compatibility - ensure frame is visible
      const seekTime = finalVideoPublicUrl ? 1.0 : 0.1
      if (el.currentTime === 0) {
        el.currentTime = seekTime
      }
    }

    const handleError = () => {
      setVideoError(true)
      setVideoLoaded(false)
    }

    el.addEventListener('loadedmetadata', handleLoadedMetadata)
    el.addEventListener('loadeddata', handleLoadedData)
    el.addEventListener('canplay', handleCanPlay)
    el.addEventListener('error', handleError)

    return () => {
      el.removeEventListener('loadedmetadata', handleLoadedMetadata)
      el.removeEventListener('loadeddata', handleLoadedData)
      el.removeEventListener('canplay', handleCanPlay)
      el.removeEventListener('error', handleError)
    }
  }, [finalVideoPublicUrl, initialSrc])

  // Use final video for preview if available, otherwise fall back to Part A
  const initialSrc = finalVideoPublicUrl || signedA || publicA

  const togglePlay = () => {
    const el = videoRef.current
    if (!el) return

    // On mobile, go fullscreen when playing (with full OnlyDuo experience)
    const isMobile = window.innerWidth < 768
    if (!isPlaying && isMobile) {
      setShowFullscreen(true)
      return
    }

    if (isPlaying) {
      el.pause()
    } else {
      el.play().catch(() => {})
      // Call onPlay callback for view tracking
      if (onPlay) {
        onPlay()
      }
    }
  }

  return (
    <>
      <div className="relative w-full" style={{ aspectRatio: '9/16' }}>
        <video
          ref={videoRef}
          key={initialSrc} // Force re-render when source changes for mobile compatibility
          className="w-full h-full bg-black rounded-lg"
          src={initialSrc}
          playsInline
          muted={muted}
          preload="metadata"
          onClick={showControls ? togglePlay : undefined}
        />

        {/* Loading state for mobile compatibility */}
        {!videoLoaded && !videoError && (
          <div className="absolute inset-0 bg-gray-900 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-600 border-t-white mx-auto mb-2"></div>
              <div className="text-xs text-gray-400">Loading preview...</div>
            </div>
          </div>
        )}

        {/* Error state */}
        {videoError && (
          <div className="absolute inset-0 bg-gray-900 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-400 text-sm mb-2">⚠️</div>
              <div className="text-xs text-gray-400">Preview unavailable</div>
            </div>
          </div>
        )}

        {/* Clean Play Button - No background overlay to show video preview */}
        {showControls && !isPlaying && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              togglePlay()
            }}
            className="absolute inset-0 flex items-center justify-center rounded-lg z-10 group"
          >
            <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-xl hover:bg-white hover:scale-110 transition-all duration-200 group-hover:shadow-2xl">
              <div className="w-0 h-0 border-l-[16px] border-l-black border-y-[12px] border-y-transparent ml-1"></div>
            </div>
          </button>
        )}

        {/* Control Buttons - Timeline Optimized */}
        {showControls && (
          <div className="absolute bottom-3 right-3 flex gap-2 z-10">
            <button
              className="bg-black/70 backdrop-blur-sm text-white text-xs rounded-full px-3 py-1.5 hover:bg-black/80 transition-colors shadow-lg"
              onClick={(e) => {
                e.stopPropagation()
                setMuted(m => !m)
              }}
            >
              {muted ? '🔇' : '🔊'}
            </button>

            <button
              className="bg-black/70 backdrop-blur-sm text-white text-xs rounded-full px-3 py-1.5 hover:bg-black/80 transition-colors shadow-lg"
              onClick={(e) => {
                e.stopPropagation()
                setShowFullscreen(true)
              }}
            >
              ⛶
            </button>
          </div>
        )}

        {/* Attribution and Part Indicators - Bottom left, above logo */}
        {isPlaying && showControls && (
          <div className="absolute bottom-12 left-2 z-10 pointer-events-none space-y-2">
            {/* Part Indicator */}
            {partB && (
              <div className="bg-black/30 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-55">
                {!playingB ? 'Part 1/2' : 'Part 2/2'}
              </div>
            )}

            {/* Attribution */}
            {(initiatorName || responderName) && (
              <div className="flex items-center gap-2 bg-black/30 text-white px-2 py-1.5 rounded backdrop-blur-sm opacity-55 max-w-xs">
                {/* Profile Photo */}
                <div className="w-5 h-5 rounded-full overflow-hidden bg-gray-300 flex-shrink-0">
                  {currentPart === 1 ? (
                    initiatorPhoto ? (
                      <img src={initiatorPhoto} alt={initiatorName || 'User'} className="w-full h-full object-cover" />
                    ) : (
                      <div className="w-full h-full bg-purple-500 flex items-center justify-center text-white text-xs font-bold">
                        {(initiatorName || 'U').charAt(0).toUpperCase()}
                      </div>
                    )
                  ) : (
                    responderPhoto ? (
                      <img src={responderPhoto} alt={responderName || 'User'} className="w-full h-full object-cover" />
                    ) : (
                      <div className="w-full h-full bg-pink-500 flex items-center justify-center text-white text-xs font-bold">
                        {(responderName || 'U').charAt(0).toUpperCase()}
                      </div>
                    )
                  )}
                </div>

                {/* Attribution Text */}
                <span className="text-xs font-medium truncate">
                  Video {currentPart} created by {currentPart === 1 ? (initiatorName || 'User') : (responderName || 'User')}
                </span>
              </div>
            )}
          </div>
        )}

        {/* OnlyDuo Watermark Logo - Left half of the space */}
        {isPlaying && showControls && (
          <div className="absolute -top-8 left-0 z-10 pointer-events-none">
            <img
              src="/oduo.png"
              alt="OnlyDuo"
              className="h-32 w-auto opacity-55 drop-shadow-xl"
            />
          </div>
        )}


      </div>

      {/* Mobile Fullscreen Modal */}
      {showFullscreen && (
        <div
          className="fixed inset-0 bg-black z-50"
          onClick={(e) => {
            // Click outside video to close
            if (e.target === e.currentTarget) {
              setShowFullscreen(false)
            }
          }}
        >
          {/* Close Button - High z-index */}
          <button
            onClick={() => setShowFullscreen(false)}
            className="absolute top-6 right-6 z-[60] bg-white/90 text-black rounded-full w-12 h-12 flex items-center justify-center shadow-lg hover:bg-white transition-colors"
          >
            <span className="text-xl font-bold">✕</span>
          </button>

          {/* Video Container - Match desktop proportions */}
          <div className="flex items-center justify-center h-full px-4">
            <div
              className="relative bg-black rounded-xl overflow-hidden shadow-lg w-80 max-w-[90vw]"
              style={{ aspectRatio: '9/16', maxHeight: '90vh' }}
            >
              {/* Mobile video - EXACT same as desktop */}
              <video
                ref={fullscreenVideoRef}
                className="w-full h-full bg-black rounded-lg"
                src={initialSrc}
                playsInline
                muted={muted}
                preload="metadata"
                webkit-playsinline="true"
                x5-playsinline="true"
                controls
                autoPlay
                onClick={(e) => e.stopPropagation()}
              />

              {/* OnlyDuo Logo - Same positioning as desktop */}
              {isPlaying && showControls && (
                <div className="absolute -top-8 left-0 z-10 pointer-events-none">
                  <img
                    src="/oduo.png"
                    alt="OnlyDuo"
                    className="h-32 w-auto opacity-55 drop-shadow-xl"
                  />
                </div>
              )}

              {/* Attribution and Part Indicators - Bottom left */}
              {isPlaying && showControls && (
                <div className="absolute bottom-12 left-2 z-10 pointer-events-none space-y-2">
                  {/* Part Indicator */}
                  {partB && (
                    <div className="bg-black/30 backdrop-blur-sm text-white text-xs px-2 py-1 rounded opacity-55">
                      {!playingB ? 'Part 1/2' : 'Part 2/2'}
                    </div>
                  )}

                  {/* Attribution */}
                  {(initiatorName || responderName) && (
                    <div className="flex items-center gap-2 bg-black/30 text-white px-2 py-1.5 rounded backdrop-blur-sm opacity-55 max-w-xs">
                      {/* Profile Photo */}
                      <div className="w-5 h-5 rounded-full overflow-hidden bg-gray-300 flex-shrink-0">
                        {currentPart === 1 ? (
                          initiatorPhoto ? (
                            <img src={initiatorPhoto} alt={initiatorName || 'User'} className="w-full h-full object-cover" />
                          ) : (
                            <div className="w-full h-full bg-purple-500 flex items-center justify-center text-white text-xs font-bold">
                              {(initiatorName || 'U').charAt(0).toUpperCase()}
                            </div>
                          )
                        ) : (
                          responderPhoto ? (
                            <img src={responderPhoto} alt={responderName || 'User'} className="w-full h-full object-cover" />
                          ) : (
                            <div className="w-full h-full bg-pink-500 flex items-center justify-center text-white text-xs font-bold">
                              {(responderName || 'U').charAt(0).toUpperCase()}
                            </div>
                          )
                        )}
                      </div>

                      {/* Attribution Text */}
                      <span className="text-xs font-medium truncate">
                        Video {currentPart} created by {currentPart === 1 ? (initiatorName || 'User') : (responderName || 'User')}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  )
}
