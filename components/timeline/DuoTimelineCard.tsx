"use client"


import { useState, useEffect } from 'react'
import { DuoVideoPlayer } from '@/components/duo/DuoVideoPlayer'
import { formatDistanceToNow } from 'date-fns'
import { Day1Badge } from '@/components/Day1Badge'
import { ReactionSystem } from '@/components/ReactionSystem'
import { DuoCompactCommentsSection } from '@/components/DuoCompactCommentsSection'

interface DuoTimelineCardProps {
  duo: {
    id: string
    initiator: { name: string; profile_picture_url?: string; has_day1_badge?: boolean; signup_number?: number }
    responder: { name: string; profile_picture_url?: string; has_day1_badge?: boolean; signup_number?: number }
    created_at: string
    final_video_url?: string
    final_video_r2_key?: string
    partA: { src_r2_key: string; hls_manifest?: string }
    partB: { src_r2_key: string; hls_manifest?: string }
    reactions_count: number
    comments_count: number
    downloads_count: number
    view_count?: number
    reactions?: Record<string, number>
    userReaction?: string | null
  }
  currentUserId?: string
}

export function DuoTimelineCard({
  duo,
  currentUserId
}: DuoTimelineCardProps) {
  const [showComments, setShowComments] = useState(false)
  const [commentCount, setCommentCount] = useState(duo.comments_count)
  const [viewCount, setViewCount] = useState(duo.view_count || 0)
  const [downloadCount, setDownloadCount] = useState(duo.downloads_count || 0)
  const timeAgo = formatDistanceToNow(new Date(duo.created_at), { addSuffix: true })

  // Debug logging for reaction issues
  useEffect(() => {
    console.log('DuoTimelineCard reaction debug:', {
      duoId: duo.id,
      userReaction: duo.userReaction,
      reactions: duo.reactions,
      currentUserId,
      finalVideoUrl: duo.final_video_url,
      finalVideoR2Key: duo.final_video_r2_key
    })
  }, [duo.id, duo.userReaction, duo.reactions, currentUserId])

  const toggleComments = () => {
    setShowComments(!showComments)
  }

  const handleCommentCountChange = (newCount: number) => {
    setCommentCount(newCount)
  }

  const handleVideoPlay = async () => {
    // Track view on every play (including replays)
    setViewCount(prev => prev + 1)

    // Track view in database
    try {
      await fetch('/api/duos/view', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoId: duo.id })
      })
    } catch (error) {
      console.error('Error tracking view:', error)
    }
  }

  const handleDownload = async () => {
    try {
      setDownloadCount(prev => prev + 1)

      // Track download in database
      await fetch('/api/duos/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoId: duo.id })
      })

      // Use the same working download logic as dashboard
      const response = await fetch('/api/duo/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duoPostId: duo.id,
          includeWatermark: true,
          includeAttribution: true
        })
      })

      if (!response.ok) {
        alert('Download failed. Please try again later.')
        return
      }

      // Get the processed video as a blob
      const blob = await response.blob()

      // Create download link
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `onlyduo-${duo.id}.mp4`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

    } catch (error) {
      alert('Download failed. Please try again later.')
    }
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-purple-200">
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex -space-x-2">
              {/* Initiator Avatar */}
              <div className="relative">
                {duo.initiator.profile_picture_url ? (
                  <img
                    src={duo.initiator.profile_picture_url}
                    alt={duo.initiator.name}
                    className="w-10 h-10 rounded-full border-3 border-white bg-gray-100 shadow-sm"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full border-3 border-white bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold shadow-sm">
                    {duo.initiator.name.charAt(0).toUpperCase()}
                  </div>
                )}
                {duo.initiator.has_day1_badge && (
                  <Day1Badge
                    signupNumber={duo.initiator.signup_number}
                    size="sm"
                    className="absolute -top-1 -right-1"
                  />
                )}
              </div>

              {/* Responder Avatar */}
              <div className="relative">
                {duo.responder.profile_picture_url ? (
                  <img
                    src={duo.responder.profile_picture_url}
                    alt={duo.responder.name}
                    className="w-10 h-10 rounded-full border-3 border-white bg-gray-100 shadow-sm"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full border-3 border-white bg-gradient-to-br from-pink-500 to-pink-600 flex items-center justify-center text-white text-sm font-bold shadow-sm">
                    {duo.responder.name.charAt(0).toUpperCase()}
                  </div>
                )}
                {duo.responder.has_day1_badge && (
                  <Day1Badge
                    signupNumber={duo.responder.signup_number}
                    size="sm"
                    className="absolute -top-1 -right-1"
                  />
                )}
              </div>
            </div>

            <div>
              <div className="font-semibold text-sm text-gray-900">
                {duo.initiator.name} & {duo.responder.name}
              </div>
              <div className="text-xs text-gray-600 flex items-center gap-1">
                <span>🎬</span>
                <span>{timeAgo}</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
            OnlyDuo
          </div>
        </div>
      </div>

      {/* Video Preview - TikTok/Instagram Style Inline Player */}
      <div className="relative group flex justify-center">
        <div
          className="relative bg-black rounded-xl mb-4 overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 w-80 max-w-[90vw]"
          style={{ aspectRatio: '9/16', maxHeight: '600px' }}
        >
          {/* Video Container - Fully Interactive */}
          <div className="absolute inset-0">
            <DuoVideoPlayer
              partA={{
                type: 'video',
                src_r2_key: duo.partA.src_r2_key,
                hls_manifest: duo.partA.hls_manifest
              }}
              partB={{
                type: 'video',
                src_r2_key: duo.partB.src_r2_key,
                hls_manifest: duo.partB.hls_manifest
              }}
              signedPlayback={false}
              showControls={true}
              initiatorName={duo.initiator.name}
              responderName={duo.responder.name}
              initiatorPhoto={duo.initiator.profile_picture_url}
              responderPhoto={duo.responder.profile_picture_url}
              onPlay={handleVideoPlay}
              finalVideoUrl={duo.final_video_url}
              finalVideoR2Key={duo.final_video_r2_key}
            />
          </div>



          {/* Bottom Info Overlay - Non-blocking */}
          <div className="absolute bottom-4 left-4 right-4 pointer-events-none z-10">
            <div className="flex items-end justify-between">
              <div className="flex flex-col gap-2">
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg w-fit opacity-55">
                  🎬 OnlyDuo
                </span>
              </div>

            </div>
          </div>
        </div>
      </div>
      {/* Engagement Bar */}
      <div className="px-4 pb-4 bg-gradient-to-r from-gray-50 to-purple-50/30">
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center gap-4 sm:gap-6">
            <ReactionSystem
              contentId={duo.id}
              contentType="duo"
              currentUserId={currentUserId}
              initialReactions={duo.reactions || {}}
              userReaction={duo.userReaction}
              onReactionUpdate={() => {}}
            />

            <button
              onClick={toggleComments}
              className="flex items-center gap-2 text-gray-600 hover:text-blue-500 hover:bg-blue-50 transition-all duration-200 hover:scale-105 px-2 py-1 rounded-full"
            >
              <span className="text-lg">💬</span>
              <span className="text-sm font-bold">{commentCount}</span>
            </button>

            <div className="flex items-center gap-2 text-gray-600 px-2 py-1">
              <span className="text-lg">👁️</span>
              <span className="text-sm font-bold">{viewCount} views</span>
            </div>

            <button
              onClick={handleDownload}
              className="flex items-center gap-2 text-gray-600 hover:text-green-500 hover:bg-green-50 transition-all duration-200 hover:scale-105 px-2 py-1 rounded-full"
            >
              <span className="text-lg">⬇️</span>
              <span className="text-sm font-bold">{downloadCount}</span>
            </button>
          </div>
        </div>

        {/* Inline Comments Section - Same as Diary Entries */}
        <DuoCompactCommentsSection
          duoId={duo.id}
          canComment={!!currentUserId}
          userId={currentUserId}
          isOpen={showComments}
          onToggle={toggleComments}
          commentCount={commentCount}
          onCommentCountChange={handleCommentCountChange}
        />
      </div>
    </div>
  )
}
